<!-- 滑动切换选项卡+吸顶演示（nvue） -->
<template>
	<!-- refresher-only为true代表只使用下拉刷新功能 -->
	<!-- hide-nvue-bottom-tag必须设置为true，因为在安卓设备中，插入的swiper cell下面，不能有其他的view -->
	<!-- @onRefresh代表监听下拉刷新事件 -->
	<z-paging ref="pagePaging" nvue-list-id="z-paging-nlist" refresher-only hide-nvue-bottom-tag @onRefresh="onRefresh">
		<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->
		<template #refresher="{refresherStatus}">
			<custom-refresher :status="refresherStatus" />
		</template>
		<!-- 这个cell是吸顶view上方跟随列表滚动的view(header) -->
		<cell>
			<view ref="header" class="banner-view" style="height: 250rpx;">
				<text style="font-size: 40rpx;font-weight: 700;color: white;">这是一个banner</text>
				<text style="font-size: 24rpx;margin-top: 5rpx;color: white;">下方tab滚动时可吸附在顶部</text>
			</view>
		</cell>
		<!-- 这个cell包含了吸顶view和主体列表，其高度必须等于页面可见高度 -->
		<cell>
			<view :style="'height:' + pageHeight + 'px'">
				<!-- 吸顶view -->
				<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
				<z-tabs ref="tabs" :list="tabList" :current="current" @change="tabsChange" />
				<!-- swiper -->
				<swiper class="swiper" :current="current" @change="swiperChange" @transition="swiperTransition" @animationfinish="swiperAnimationfinish">
					<swiper-item class="swiper-item" v-for="(item, index) in tabList" :key="index">
						<!-- 这里的sticky-swiper-item-n为demo中为演示用定义的组件，列表及分页代码在sticky-swiper-item-n组件内 -->
						<!-- 请注意，sticky-swiper-item-n非z-paging内置组件，在自己的项目中必须自己创建，若未创建则会报组件不存在的错误 -->
						<sticky-swiper-item-n ref="swiperItem" :tabIndex="index" :currentIndex="current">
						</sticky-swiper-item-n>
					</swiper-item>
				</swiper>
			</view>
		</cell>
	</z-paging>
</template>

<script>
	// #ifdef APP-NVUE
	const dom = weex.requireModule('dom')
	// #endif
	export default {
		data() {
			return {
				tabList: ['测试1', '测试2', '测试3', '测试4'],
				// 页面高度
				pageHeight: 0,
				// header高度
				headerHeight: 0,
				current: 0, // tabs组件的current值，表示当前活动的tab选项
			}
		},
		onLoad() {
			// 获取页面可见高度，除去header以外的高度必须等于页面可见高度！
			// 注意：如果是自定义导航栏通过slot="top"插入的情况，这里的this.pageHeight = uni.getSystemInfoSync().windowHeight - 自定义导航栏高度。底部tabbar亦然
			this.pageHeight = uni.getSystemInfoSync().windowHeight;
			this.$nextTick(() => {
				// 获取header的高度
				dom.getComponentRect(this.$refs.header, option => {
					if (option && option.result) {
						this.headerHeight = option.size.height;
					}
				})
			})
		},
		watch: {
			// 当current或者headerHeight改变的时候，调用子组件的设置嵌套list父容器支持swiper-list吸顶滚动效果的方法
			current: {
                handler() {
                    this.setListSpecialEffects();
                },
				immediate: true
			},
			// 当current或者headerHeight改变的时候，调用子组件的设置嵌套list父容器支持swiper-list吸顶滚动效果的方法
			headerHeight() {
				this.setListSpecialEffects();
			}
		},
		methods: {
			onRefresh() {
				// 触发了下拉刷新，通知当前tabIndex对应子组件的下拉刷新
				this.$refs.swiperItem[this.current].reload(() => {
					//当当前显示的列表刷新结束，结束当前页面的刷新状态
					this.$refs.pagePaging.endRefresh();
				});
			},
			// tabs通知swiper切换
			tabsChange(index) {
				this.current = index;
			},
			// swiper change时触发
			swiperChange(e) {
				// #ifndef APP-PLUS || H5 || MP-WEIXIN || MP-QQ
				let current = e.target.current || e.detail.current;
				this.tabsChange(current);
				// #endif
			},
			// swiper滑动中
			swiperTransition(e) {
				this.$refs.tabs.setDx(e.detail.dx);
			},
			// swiper滑动结束
			swiperAnimationfinish(e) {
				this.current = e.detail.current;
				this.$refs.tabs.unlockDx();
			},
			// 设置子组件嵌套list父容器支持swiper-list吸顶滚动效果的方法
			setListSpecialEffects() {
				this.$nextTick(() => {
					this.$refs.swiperItem && this.$refs.swiperItem[this.current].setSpecialEffects(this.headerHeight);
				})
			}
		}
	}
</script>

<style>
	.banner-view {
		background-color: #007AFF;
		align-items: center;
		justify-content: center;
	}

	.swiper,
	.swiper-item,
	.swiper-item-content {
		flex: 1;
	}
</style>
