<!-- 聊天记录模式演示(nvue)，加载更多聊天记录无闪动 -->
<template>
	<view class="content">
		<!-- use-chat-record-mode：开启聊天记录模式 -->
		<!-- safe-area-inset-bottom：开启底部安全区域适配 -->
		<!-- bottom-bg-color：设置slot="bottom"容器的背景色，这里设置为和chat-input-bar的背景色一致 -->
		<z-paging ref="paging" v-model="dataList" use-chat-record-mode safe-area-inset-bottom bottom-bg-color="#f8f8f8"
		@query="queryList" @cellStyleChange="cellStyleChange" @keyboardHeightChange="keyboardHeightChange">
			<!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
			
			<!-- 在nvue中，z-paging中插入的列表item必须是cell，必须使用cell包住，因为在nvue中，z-paging使用的是nvue的list组件。 -->
			<!-- 不能使用index作为key的唯一标识，:key必须添加并且必须是唯一的 -->
			<!-- :style="cellStyle"必须写，否则会导致列表倒置（必须写在for循环标签上，不得写在容器上）！！！ -->
			<!-- 如果希望在vue中渲染view，nvue中渲染cell，可使用z-paging-cell代替cell.聊天记录nvue和vue写法有些许不同，请留意 -->
			<cell class="item" @touchstart="touchstart" v-for="(item,index) in dataList" :key="item.title" :style="cellStyle">
				<!-- 聊天item -->
				<chat-item :item="item"></chat-item>
			</cell>
			<!-- 底部聊天输入框 -->
			<template #bottom>
				<chat-input-bar ref="inputBar" @send="doSend" />
			</template>
		</z-paging>
	</view>
</template>

<script>
	import request from '../../http/request.js'
	export default {
		data() {
			return {
				// v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
				dataList: [],
				// 当前cell旋转的style，必须写
				cellStyle: {}
			}
		},
		methods: {
			queryList(pageNo, pageSize) {
				// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
				// 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
				// 模拟请求服务器获取分页数据，请替换成自己的网络请求
				const params = {
					pageNo: pageNo,
					pageSize: pageSize,
				}
				request.queryChatList(params).then(res => {
					// 将请求的结果数组传递给z-paging
					this.$refs.paging.complete(res.data.list);
				}).catch(res => {
					// 如果请求失败写this.$refs.paging.complete(false);
					// 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
					// 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
					this.$refs.paging.complete(false);
				})
			},
			// 监听cellStyle改变，这个方法必须写！！
			cellStyleChange(style) {
				this.cellStyle = style;
			},
			// 监听键盘高度改变，请不要直接通过uni.onKeyboardHeightChange监听，否则可能导致z-paging内置的键盘高度改变监听失效（如果不需要切换表情面板则不用写）
			keyboardHeightChange(res) {
				this.$refs.inputBar.updateKeyboardHeightChange(res);
			},
			// 触摸列表cell时，隐藏键盘。(为什么不放在list内部处理？因为在list上或其父view上添加@touchstart可能引发列表被锁住无法滚动等一系列问题)
			touchstart() {
				uni.hideKeyboard();
				// 通知chatInputBar隐藏表情面板
				this.$refs.inputBar.hidedKeyboard();
			},
			// 发送消息
			doSend(msg) {
				uni.showLoading({
					title: '发送中...'
				})
				setTimeout(()=>{
					uni.hideLoading();
					this.$refs.paging.addChatRecordData({
						time: '',
						icon: '/static/daxiong.jpg',
						name: '大雄',
						content: msg,
						isMe: true
					});
				},500)
			}
		}
	}
</script>

<style>
	
</style>
