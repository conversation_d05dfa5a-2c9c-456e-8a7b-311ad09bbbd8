<template>
	<view class="content">
		<z-paging ref="paging" refresher-only @onRefresh="onRefresh">
			
			<!-- 自定义导航栏 -->
			<template #top>
				<index-nav />
			</template>
			
			<!-- 自定义下拉刷新view -->
			<template #refresher>
				<view style="height: 120rpx;display: flex;justify-content: center;align-items: center;">
					<image style="width: 300rpx;height: 60rpx;" src="../../static/logo_loading.gif"></image>
				</view>
			</template>
			
			<view class="demo-list">
				<view class="demo-item" v-for="(item,index) in list" :key="index" @click="itemClick(item)">
					<view class="demo-item-main">
						<view class="demo-item-title">{{item.title}}</view>
						<view class="demo-item-subtitle" v-if="item.subTitle.length">{{item.subTitle}}</view>
						<view class="demo-item-file">
							<text v-if="item.title.indexOf('nvue')!==-1" style="background-color: #01c301;">{{item.file + '.nvue'}}</text>
							<text v-else-if="item.title.indexOf('vue3')!==-1" style="background-color: #f96027;">{{item.file + '.vue'}}</text>
							<text v-else>{{item.file + '.vue'}}</text>
							
							<view v-if="showSourceViewBtn" class="demo-item-file-source" @click.stop="sourceViewClick(item)">
								<text>查看源码</text>
								<image src="../../static/github.png"></image>
							</view>
						</view>
					</view>
					<image class="demo-item-more-img" src="../../static/more_icon.png"></image>
				</view>
			</view>
			<!-- #ifndef APP-PLUS -->
			<view class="demo-nvue-tip">- 将此demo运行至App上可体验nvue页面效果 -</view>
			<!-- #endif -->
		</z-paging>
	</view>
</template>

<script>
	import indexList from './list'
	export default {
		data() {
			return {
				list: indexList.list
			}
		},
		mounted() {
			// #ifdef APP-PLUS
			this.list = this.list.concat(indexList.listNvue);
			// #endif
		},
		computed: {
			showSourceViewBtn() {
				// #ifdef H5
				return window.location.host === 'demo.z-paging.zxlee.cn';
				// #endif
				return false;
			}
		},
		methods: {
			// 下拉刷新被触发
			onRefresh() {
				// 以告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
				setTimeout(() => {
					//1.5秒之后停止刷新动画
					this.$refs.paging.complete();
				}, 1500)
			},
			// 点击了查看源码
			sourceViewClick(item) {
				const sourcePrefix = 'https://github.com/SmileZXLee/uni-z-paging/tree/main/demo/z-paging-demo/pages';
				const url = `${sourcePrefix}/${item.file}/${item.file}${item.title.indexOf('nvue') !== -1 ? '.nvue' : '.vue'}`;
				// #ifdef H5
				window.open(url);
				// #endif
			},
			// 点击了item
			itemClick(item) {
				uni.navigateTo({
					url: `../${item.file}/${item.file}`
				})
			}
		}
	}
</script>

<style scoped>
	.demo-item {
		display: flex;
		align-items: center;
		border-bottom: #eeeeee solid 1px;
		padding: 20rpx 30rpx;
	}

	.demo-item-main {
		flex: 1;
		margin-right: 20rpx;
	}

	.demo-item-main>view:not(:last-child) {
		margin-bottom: 10rpx;
	}

	.demo-item-more-img {
		width: 24rpx;
		height: 24rpx;
	}

	.demo-item-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.demo-item-subtitle {
		font-size: 26rpx;
		color: #666666;
	}
	
	.demo-item-file {
		display: flex;
		align-items: center;
	}

	.demo-item-file > text {
		background-color: #007AFF;
		color: white;
		font-size: 24rpx;
		padding: 5rpx 10rpx;
		border-radius: 8rpx;
	}
	.demo-item-file-source {
		margin-left: 10rpx;
		background-color: #fc0c52;
		font-size: 24rpx;
		padding: 5rpx 10rpx;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
	}
	.demo-item-file-source > text {
		color: white;
		font-size: 24rpx;
	}
	.demo-item-file-source > image {
		width: 26rpx;
		height: 26rpx;
		margin-left: 5rpx;
	}

	.demo-nvue-tip {
		width: 100%;
		padding: 20rpx;
		color: #aaaaaa;
		font-size: 24rpx;
		text-align: center;
	}

	.tip-bottom {
		background-color: #007AFF;
		color: white;
		padding: 20rpx 0rpx;
		text-align: center;
	}
</style>
